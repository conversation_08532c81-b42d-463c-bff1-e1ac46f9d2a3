# AI Integration: OpenAI + Google Gemini 2.5 Flash Preview

## Overview

The binary options trading bot now supports **dual AI providers**:
- **OpenAI GPT-4** (existing)
- **Google Gemini 2.5 Flash Preview** (new)

The system automatically detects which API keys are available and uses the best provider for analysis.

## Features

### 🤖 Automatic Provider Detection
- The bot checks for available API keys on startup
- **Gemini is preferred** when both keys are available (better reasoning capabilities)
- **Seamless fallback** to OpenAI if only OpenAI key is provided
- **Error handling** if no API keys are found

### 🧠 Enhanced AI Analysis
- **Gemini 2.5 Flash Preview** offers superior reasoning and analysis
- **Consistent output format** regardless of provider
- **Same trading signal structure** for seamless integration
- **Provider information** included in analysis results

## Configuration

### Environment Variables

Add to your `.env` file:

```bash
# AI API Configuration (Use either OpenAI or Gemini - bot will auto-detect)

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=1000
OPENAI_TEMPERATURE=0.3

# Google Gemini Configuration
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-2.5-flash-preview-05-20
GEMINI_MAX_TOKENS=1000
GEMINI_TEMPERATURE=0.3
```

### Getting API Keys

#### OpenAI API Key
1. Visit: https://platform.openai.com/api-keys
2. Create a new API key
3. Add to `.env` file as `OPENAI_API_KEY`

#### Google Gemini API Key
1. Visit: https://aistudio.google.com/app/apikey
2. Create a new API key
3. Add to `.env` file as `GEMINI_API_KEY`

## Installation

### New Dependency
The integration adds the Google Gemini library:

```bash
pip install google-genai==1.0.0
```

Or install all dependencies:
```bash
pip install -r requirements.txt
```

## Usage

### Automatic Provider Selection
The bot automatically selects the best available provider:

1. **Both keys available**: Uses Gemini (preferred)
2. **Only OpenAI key**: Uses OpenAI
3. **Only Gemini key**: Uses Gemini
4. **No keys**: Shows error message

### Testing the Integration
Run the test script to verify everything works:

```bash
python test_ai_integration.py
```

### Running the Bot
No changes needed - just run as usual:

```bash
python binary_options_bot.py
```

The bot will automatically detect and use the available AI provider.

## Technical Details

### Provider Detection Logic
```python
def _determine_ai_provider(self) -> str:
    # Prefer Gemini if available (better performance)
    if GEMINI_AVAILABLE and DerivConfig.GEMINI_API_KEY:
        return "gemini"
    elif DerivConfig.OPENAI_API_KEY:
        return "openai"
    else:
        return "none"
```

### Analysis Output
Both providers return the same JSON structure:
```json
{
    "signal": "CALL" | "PUT" | "NO TRADE",
    "confidence": 0.75,
    "reasoning": "Detailed analysis...",
    "key_factors": ["factor1", "factor2"],
    "risk_assessment": "LOW" | "MEDIUM" | "HIGH",
    "provider": "gemini" | "openai",
    "model_used": "gemini-2.5-flash-preview-05-20",
    "timestamp": "2025-01-XX..."
}
```

## Benefits

### Why Gemini 2.5 Flash Preview?
- **Enhanced reasoning**: Better at complex market analysis
- **Improved accuracy**: More sophisticated pattern recognition
- **Better context understanding**: Superior handling of multi-modal data
- **Advanced thinking capabilities**: Built-in reasoning processes

### Backward Compatibility
- **No breaking changes**: Existing OpenAI users unaffected
- **Same interface**: All methods work identically
- **Gradual migration**: Can switch providers anytime
- **Fallback support**: Always works with at least one provider

## Troubleshooting

### Common Issues

1. **Import Error**: Install `google-genai` package
2. **API Key Error**: Check `.env` file configuration
3. **Provider Not Found**: Ensure at least one API key is set
4. **Rate Limits**: Both providers have different rate limits

### Logs
Check the logs for provider selection:
```
[AI] Using Google Gemini: gemini-2.5-flash-preview-05-20
[AI] Analyzing market data for EURUSD using gemini
```

## Future Enhancements

- Support for additional AI providers
- Provider-specific optimizations
- Advanced model selection logic
- Performance comparison metrics
