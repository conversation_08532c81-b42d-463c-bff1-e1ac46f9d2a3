# Binary Options Trading Bot - Deriv.com Integration

This trading bot has been updated to support **Binary Options trading** using the Deriv.com API. The bot now provides AI-powered analysis specifically designed for binary options strategies.

## 🚀 New Features

### Binary Options Support
- **Real-time trading** via Deriv WebSocket API
- **AI analysis** with 25+ technical indicators (RSI, MACD, Bollinger Bands)
- **Advanced technical analysis** including trend, momentum, and volatility indicators
- **Multiple contract types**: CALL/PUT options
- **Risk management** with configurable limits
- **Synthetic indices** and Forex pairs support

### Technical Indicators Integration
- **Trend Indicators**: SMA, EMA, MACD, Parabolic SAR
- **Momentum Indicators**: RSI, Stochastic, Williams %R, CCI
- **Volatility Indicators**: Bollinger Bands, ATR, Keltner Channels
- **Volume Indicators**: OBV, VWAP (when available)
- **Smart Analysis**: AI interprets all indicators for trading signals

### Supported Instruments
- **Synthetic Indices**: R_10, R_25, R_50, R_75, R_100 (Volatility indices)
- **Forex Pairs**: EURUSD, GBPUSD, USDJPY, AUDUSD, etc.
- **Duration Options**: 1-10 ticks or 1-10 minutes

## 📋 Prerequisites

1. **Deriv Account**: Create a free account at [deriv.com](https://deriv.com)
2. **API Token**: Generate your API token at [app.deriv.com/account/api-token](https://app.deriv.com/account/api-token)
3. **OpenAI API Key**: Get your key from [platform.openai.com](https://platform.openai.com/api-keys)

## ⚙️ Setup Instructions

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Configure Environment
Copy the example environment file and add your credentials:
```bash
cp .env.example .env
```

Edit `.env` with your actual values:
```env
# Required for Binary Options
DERIV_API_TOKEN=your_deriv_api_token_here
DERIV_APP_ID=1089
OPENAI_API_KEY=your_openai_api_key_here

# Trading Configuration
DEFAULT_STAKE=1.0
DEFAULT_DURATION=5
DEFAULT_DURATION_UNIT=t
MAX_STAKE_PER_TRADE=10.0
MAX_DAILY_LOSS=100.0
MAX_CONCURRENT_TRADES=3

# Symbols to trade
BINARY_OPTIONS_SYMBOLS=R_10,R_25,R_50,EURUSD,GBPUSD
```

### 3. Run the Binary Options Bot

#### Single Analysis
```bash
python binary_options_bot.py --symbol R_10
```

#### Continuous Trading
```bash
python binary_options_bot.py --continuous --interval 1
```

#### With Custom Context
```bash
python binary_options_bot.py --symbol EURUSD --context "Focus on EUR strength"
```

## 🎯 How It Works

### 1. Market Data Collection
- **Real-time ticks** from Deriv WebSocket API
- **Historical data** for technical analysis
- **Synthetic indices** with controlled volatility

### 2. AI Analysis
- **Specialized prompts** for binary options
- **Short-term momentum** analysis
- **Volatility-based** duration recommendations
- **Risk assessment** for each trade

### 3. Signal Generation
- **CALL signals**: Price expected to rise
- **PUT signals**: Price expected to fall
- **Duration optimization**: Based on volatility and confidence
- **Risk management**: Automatic position sizing

### 4. Trade Execution
- **Automated trading** via Deriv API
- **Real-time monitoring** of open positions
- **Early exit** capabilities
- **P&L tracking** and reporting

## 📊 Trading Strategies

### Volatility Index Trading
- **R_10**: Low volatility, longer durations
- **R_25**: Medium volatility, balanced approach
- **R_50**: High volatility, shorter durations
- **R_75/R_100**: Very high volatility, tick-based trading

### Forex Trading
- **Major pairs**: EURUSD, GBPUSD, USDJPY
- **Economic events**: News-based analysis
- **Technical patterns**: Support/resistance levels

## 🛡️ Risk Management

### Built-in Safeguards
- **Maximum stake per trade**: Configurable limit
- **Daily loss limit**: Stop trading when reached
- **Concurrent trades limit**: Prevent overexposure
- **Confidence thresholds**: Only trade high-confidence signals

### Configuration Options
```env
MAX_STAKE_PER_TRADE=10.0      # Maximum amount per trade
MAX_DAILY_LOSS=100.0          # Stop trading at this loss
MAX_CONCURRENT_TRADES=3       # Maximum open positions
```

## 📈 Performance Monitoring

### Real-time Metrics
- **Win rate**: Percentage of profitable trades
- **Daily P&L**: Current profit/loss
- **Active contracts**: Number of open positions
- **Trade history**: Detailed logging

### Example Output
```
Binary Options Signal - R_10
┌─────────────┬─────────────┐
│ Property    │ Value       │
├─────────────┼─────────────┤
│ Signal      │ CALL        │
│ Confidence  │ 78.5%       │
│ Current Price│ 1234.567   │
│ Risk Level  │ MEDIUM      │
│ Duration    │ 5 t         │
└─────────────┴─────────────┘
```

## 🔧 Advanced Configuration

### Custom Symbols
Add your preferred symbols to the configuration:
```env
BINARY_OPTIONS_SYMBOLS=R_10,R_25,EURUSD,GBPUSD,USDJPY
```

### Analysis Parameters
```env
TICK_HISTORY_COUNT=100        # Historical ticks for analysis
CANDLE_HISTORY_COUNT=50       # Historical candles
ANALYSIS_INTERVAL_SECONDS=30  # Real-time analysis frequency
```

### Sound Notifications
Configure audio alerts for high-confidence trading signals:
```env
ENABLE_SOUND_NOTIFICATIONS=true    # Enable/disable sound alerts
SOUND_NOTIFICATION_THRESHOLD=0.70  # Minimum confidence for sound (70%)
```

**Features:**
- **Audio alerts** ONLY for CALL/PUT signals with 70%+ confidence
- **No sounds** for signals below threshold or NO_TRADE signals
- **Custom sounds** support (place WAV files in `sounds/` directory)
- **System fallback** uses system beep if no sound files found
- **Non-blocking** notifications don't interrupt trading

**Sound Files** (optional):
- `sounds/call_signal.wav` - CALL signal notification
- `sounds/put_signal.wav` - PUT signal notification
- `sounds/trade_signal.wav` - Generic trading signal

**Testing:**
```bash
python test_notifications.py
```

## 🚨 Important Notes

### Demo vs Real Trading
- **Demo account**: Use for testing and learning
- **Real account**: Only use with proper risk management
- **Start small**: Begin with minimum stakes

### Market Hours
- **Synthetic indices**: Available 24/7
- **Forex pairs**: Follow market hours
- **Volatility**: Higher during market overlaps

### Legal Disclaimer
- **Educational purpose**: This bot is for learning and research
- **Risk warning**: Binary options trading involves significant risk
- **Regulation**: Ensure compliance with local regulations

## 🆘 Troubleshooting

### Common Issues

1. **Connection Failed**
   - Check your internet connection
   - Verify DERIV_API_TOKEN is correct
   - Ensure Deriv account is active

2. **Authentication Error**
   - Generate a new API token
   - Check token permissions (trading scope)
   - Verify account status

3. **No Market Data**
   - Check symbol spelling
   - Verify market is open (for Forex)
   - Try synthetic indices (always available)

### Support
- **Deriv API Documentation**: [developers.deriv.com](https://developers.deriv.com)
- **Community**: [community.deriv.com](https://community.deriv.com)
- **Support**: [deriv.com/contact-us](https://deriv.com/contact-us)

## 📚 Additional Resources

### Learning Materials
- **Binary Options Guide**: Understanding the basics
- **Technical Analysis**: Chart patterns and indicators
- **Risk Management**: Position sizing and money management

### API Documentation
- **Deriv API**: Complete WebSocket API reference
- **Binary Options**: Contract types and parameters
- **Market Data**: Real-time ticks and historical data

---

**⚠️ Risk Warning**: Binary options trading involves substantial risk of loss. Only trade with money you can afford to lose. Past performance does not guarantee future results.
