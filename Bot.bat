@echo off
title Binary Options Trading Bot - Deriv.com
color 0A

:MAIN_MENU
cls
echo.
echo ========================================
echo   BINARY OPTIONS TRADING BOT - DERIV
echo ========================================
echo.
echo Choose an option:
echo.
echo 1. Test Connection
echo 2. Interactive Trading Bot (NEW!)
echo 3. Quick Analysis - R_10 (1min)
echo 4. Quick Analysis - R_25 (5min)
echo 5. Quick Analysis - EURUSD (15min)
echo 6. View Configuration
echo 0. Exit
echo.
echo NOTE: Option 2 provides full interactive experience
echo       with symbol and timeframe selection!
echo.
set /p choice="Enter your choice (0-6): "

if "%choice%"=="1" goto TEST_CONNECTION
if "%choice%"=="2" goto INTERACTIVE_BOT
if "%choice%"=="3" goto QUICK_R10
if "%choice%"=="4" goto QUICK_R25
if "%choice%"=="5" goto QUICK_EURUSD
if "%choice%"=="6" goto VIEW_CONFIG
if "%choice%"=="0" goto EXIT
goto INVALID_CHOICE

:TEST_CONNECTION
cls
echo.
echo ========================================
echo        TESTING DERIV CONNECTION
echo ========================================
echo.
echo Testing your Deriv API connection...
echo This will verify your API token and connectivity.
echo.
python test_deriv_connection.py
echo.
pause
goto MAIN_MENU

:INTERACTIVE_BOT
cls
echo.
echo ========================================
echo      INTERACTIVE TRADING BOT
echo ========================================
echo.
echo Starting the interactive binary options bot...
echo.
echo Features:
echo - Choose from all available symbols
echo - Select your preferred timeframe
echo - AI-powered analysis with 25+ technical indicators
echo - RSI, MACD, Bollinger Bands, Moving Averages
echo - Real-time trading execution
echo.
echo The bot will guide you through:
echo 1. Symbol selection (Synthetic indices, Forex pairs)
echo 2. Timeframe selection (1min, 5min, 15min, 1hr, tick data)
echo 3. AI analysis with technical indicators and timeframe insights
echo 4. Optional trade execution
echo.
python binary_options_bot.py
echo.
pause
goto MAIN_MENU

:QUICK_R10
cls
echo.
echo ========================================
echo     QUICK ANALYSIS - R_10 (1MIN)
echo ========================================
echo.
echo Running quick analysis on Volatility 10 Index...
echo Timeframe: 1-minute candles
echo.
echo NOTE: For full interactive experience with symbol
echo       and timeframe selection, use option 2!
echo.
python -c "
from binary_options_bot import BinaryOptionsBot
import time
bot = BinaryOptionsBot()
if bot.start():
    try:
        print('Running R_10 analysis with 1-minute timeframe...')
        time.sleep(2)
        result = bot.analyze_symbol('R_10', '', 60, 50)
        if result:
            bot.trader.update_active_contracts()
            summary = bot.trader.get_trading_summary()
            print(f'\\nTrading Summary: {summary}')
    finally:
        bot.stop()
else:
    print('Failed to start bot')
"
echo.
pause
goto MAIN_MENU

:QUICK_R25
cls
echo.
echo ========================================
echo     QUICK ANALYSIS - R_25 (5MIN)
echo ========================================
echo.
echo Running quick analysis on Volatility 25 Index...
echo Timeframe: 5-minute candles
echo.
echo NOTE: For full interactive experience with symbol
echo       and timeframe selection, use option 2!
echo.
python -c "
from binary_options_bot import BinaryOptionsBot
import time
bot = BinaryOptionsBot()
if bot.start():
    try:
        print('Running R_25 analysis with 5-minute timeframe...')
        time.sleep(2)
        result = bot.analyze_symbol('R_25', '', 300, 50)
        if result:
            bot.trader.update_active_contracts()
            summary = bot.trader.get_trading_summary()
            print(f'\\nTrading Summary: {summary}')
    finally:
        bot.stop()
else:
    print('Failed to start bot')
"
echo.
pause
goto MAIN_MENU

:QUICK_EURUSD
cls
echo.
echo ========================================
echo    QUICK ANALYSIS - EURUSD (15MIN)
echo ========================================
echo.
echo Running quick analysis on EUR/USD...
echo Timeframe: 15-minute candles
echo.
echo NOTE: For full interactive experience with symbol
echo       and timeframe selection, use option 2!
echo.
python -c "
from binary_options_bot import BinaryOptionsBot
import time
bot = BinaryOptionsBot()
if bot.start():
    try:
        print('Running EURUSD analysis with 15-minute timeframe...')
        time.sleep(2)
        result = bot.analyze_symbol('EURUSD', '', 900, 50)
        if result:
            bot.trader.update_active_contracts()
            summary = bot.trader.get_trading_summary()
            print(f'\\nTrading Summary: {summary}')
    finally:
        bot.stop()
else:
    print('Failed to start bot')
"
echo.
pause
goto MAIN_MENU

:VIEW_CONFIG
cls
echo.
echo ========================================
echo        CONFIGURATION STATUS
echo ========================================
echo.
echo Checking your configuration...
echo.
if exist .env (
    echo ✓ .env file found
    echo.
    echo Configuration preview:
    findstr /B "DERIV_APP_ID=" .env 2>nul || echo   DERIV_APP_ID: Not set
    findstr /B "DERIV_API_TOKEN=" .env 2>nul | findstr /V "your_deriv_api_token_here" >nul && echo   DERIV_API_TOKEN: ✓ Set || echo   DERIV_API_TOKEN: ❌ Not configured
    findstr /B "OPENAI_API_KEY=" .env 2>nul | findstr /V "your_openai_api_key_here" >nul && echo   OPENAI_API_KEY: ✓ Set || echo   OPENAI_API_KEY: ❌ Not configured
    findstr /B "DEFAULT_STAKE=" .env 2>nul || echo   DEFAULT_STAKE: Using default
    findstr /B "BINARY_OPTIONS_SYMBOLS=" .env 2>nul || echo   BINARY_OPTIONS_SYMBOLS: Using default
) else (
    echo ❌ .env file not found!
    echo.
    echo Please copy .env.example to .env and configure your API keys.
)
echo.
echo For setup instructions, see BINARY_OPTIONS_README.md
echo.
pause
goto MAIN_MENU

:INVALID_CHOICE
cls
echo.
echo ========================================
echo           INVALID CHOICE
echo ========================================
echo.
echo Please enter a number between 0 and 6.
echo.
echo Available options:
echo 1 - Test Connection
echo 2 - Interactive Trading Bot (RECOMMENDED)
echo 3 - Quick Analysis R_10
echo 4 - Quick Analysis R_25
echo 5 - Quick Analysis EURUSD
echo 6 - View Configuration
echo 0 - Exit
echo.
timeout /t 3 >nul
goto MAIN_MENU

:EXIT
cls
echo.
echo ========================================
echo        BINARY OPTIONS BOT EXIT
echo ========================================
echo.
echo Thank you for using the Interactive Binary Options Trading Bot!
echo.
echo Enhanced Features You Experienced:
echo ✓ Interactive symbol selection from live market data
echo ✓ Multiple timeframe options (1min, 5min, 15min, 1hr, tick data)
echo ✓ AI analysis with 25+ technical indicators (RSI, MACD, Bollinger Bands)
echo ✓ Advanced technical analysis with trend and momentum signals
echo ✓ Real-time trading with Deriv API
echo.
echo Remember:
echo - Always test with demo accounts first
echo - Binary options trading involves significant risk
echo - Only trade with money you can afford to lose
echo - Use the interactive mode (option 2) for best experience
echo.
echo Visit https://developers.deriv.com for more information.
echo.
timeout /t 4 >nul
exit
