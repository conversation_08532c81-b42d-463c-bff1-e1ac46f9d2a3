"""
Notification Sound Module
Handles audio notifications for trading signals
"""
import os
import sys
import threading
from typing import Optional
from logger_config import setup_logger

class NotificationSound:
    """Handle sound notifications for trading signals"""
    
    def __init__(self, enabled: bool = True):
        self.logger = setup_logger("NotificationSound")
        self.enabled = enabled
        self._sound_available = self._check_sound_availability()
        
    def _check_sound_availability(self) -> bool:
        """Check if sound functionality is available"""
        try:
            # Try importing playsound
            import playsound
            return True
        except ImportError:
            self.logger.warning("playsound library not available - sound notifications disabled")
            return False
        except Exception as e:
            self.logger.warning(f"Sound system not available: {e}")
            return False
    
    def _play_system_beep(self):
        """DISABLED - No system beep fallback"""
        # System beep fallback is disabled - only play custom sound files
        pass
    
    def _play_sound_file_pygame(self, sound_file: str):
        """Play sound file using pygame - more reliable for custom WAV files"""
        try:
            import pygame

            # Convert to absolute path
            abs_path = os.path.abspath(sound_file)
            self.logger.info(f"Playing custom sound file with pygame: {abs_path}")

            if not os.path.exists(abs_path):
                self.logger.error(f"Custom sound file not found: {abs_path}")
                return False

            if os.path.getsize(abs_path) == 0:
                self.logger.error(f"Custom sound file is empty: {abs_path}")
                return False

            # Initialize pygame mixer if not already initialized
            if not pygame.mixer.get_init():
                pygame.mixer.init()
                self.logger.info("Initialized pygame mixer")

            # Load and play the sound
            sound = pygame.mixer.Sound(abs_path)
            sound.play()
            self.logger.info(f"Successfully played custom sound file with pygame: {abs_path}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to play custom sound file with pygame {sound_file}: {e}")

            # Fallback to playsound if pygame fails
            try:
                import playsound
                self.logger.info(f"Trying playsound fallback for: {abs_path}")
                playsound.playsound(abs_path, block=False)
                self.logger.info(f"Successfully played with playsound fallback: {abs_path}")
                return True
            except Exception as e2:
                self.logger.error(f"Playsound fallback also failed: {e2}")
                return False

    def _play_sound_file(self, sound_file: str):
        """Play ONLY custom sound files using pygame - no Windows fallbacks"""
        return self._play_sound_file_pygame(sound_file)
    
    def _play_notification_async(self, signal_type: str):
        """Play ONLY custom sound files - no system fallbacks"""
        try:
            # Only play custom sound files - no fallbacks
            sound_files = {
                'CALL': 'sounds/call_signal.wav',
                'PUT': 'sounds/put_signal.wav',
            }

            self.logger.info(f"Playing custom notification for signal type: {signal_type}")

            # Try specific sound file for signal type
            if signal_type in sound_files:
                self.logger.info(f"Attempting to play custom {signal_type} sound file")
                if self._play_sound_file(sound_files[signal_type]):
                    self.logger.info(f"Successfully played custom {signal_type} sound file")
                    return
                else:
                    self.logger.error(f"Failed to play custom {signal_type} sound file")

            # NO SYSTEM FALLBACK - only custom sounds or silence
            self.logger.warning(f"No custom sound file available for {signal_type} - notification will be silent")

        except Exception as e:
            self.logger.error(f"Custom notification sound failed: {e}")
    
    def play_signal_notification(self, signal_type: str, confidence: float, threshold: float = 0.70):
        """
        Play notification sound for trading signal

        Args:
            signal_type: Type of signal ('CALL', 'PUT', etc.)
            confidence: Signal confidence level (0.0 to 1.0)
            threshold: Minimum confidence threshold for playing sound
        """
        if not self.enabled or not self._sound_available:
            return

        # Only play notification for signals above the specified threshold
        if confidence < threshold:
            return
        
        try:
            # Play sound in separate thread to avoid blocking the main application
            sound_thread = threading.Thread(
                target=self._play_notification_async,
                args=(signal_type,),
                daemon=True
            )
            sound_thread.start()
            
            self.logger.info(f"Playing notification sound for {signal_type} signal ({confidence:.1%} confidence)")
            
        except Exception as e:
            self.logger.error(f"Failed to play notification sound: {e}")
    
    def enable(self):
        """Enable sound notifications"""
        self.enabled = True
        self.logger.info("Sound notifications enabled")
    
    def disable(self):
        """Disable sound notifications"""
        self.enabled = False
        self.logger.info("Sound notifications disabled")
    
    def is_enabled(self) -> bool:
        """Check if sound notifications are enabled"""
        return self.enabled and self._sound_available
    
    def test_sound(self):
        """Test sound notification system"""
        if not self._sound_available:
            print("Sound system not available")
            return False
        
        print("Testing notification sound...")
        try:
            self._play_notification_async('TEST')
            print("Sound test completed")
            return True
        except Exception as e:
            print(f"Sound test failed: {e}")
            return False

# Global notification sound instance
_notification_sound = None

def get_notification_sound() -> NotificationSound:
    """Get global notification sound instance"""
    global _notification_sound
    if _notification_sound is None:
        _notification_sound = NotificationSound()
    return _notification_sound

def play_signal_notification(signal_type: str, confidence: float, threshold: float = 0.70):
    """Convenience function to play signal notification"""
    notification_sound = get_notification_sound()
    notification_sound.play_signal_notification(signal_type, confidence, threshold)

def enable_notifications():
    """Enable sound notifications"""
    notification_sound = get_notification_sound()
    notification_sound.enable()

def disable_notifications():
    """Disable sound notifications"""
    notification_sound = get_notification_sound()
    notification_sound.disable()

def test_notification_sound():
    """Test the notification sound system"""
    notification_sound = get_notification_sound()
    return notification_sound.test_sound()
