"""
Notification Sound Module
Handles audio notifications for trading signals
"""
import os
import sys
import threading
from typing import Optional
from logger_config import setup_logger

class NotificationSound:
    """Handle sound notifications for trading signals"""
    
    def __init__(self, enabled: bool = True):
        self.logger = setup_logger("NotificationSound")
        self.enabled = enabled
        self._sound_available = self._check_sound_availability()
        
    def _check_sound_availability(self) -> bool:
        """Check if sound functionality is available"""
        try:
            # Try importing playsound
            import playsound
            return True
        except ImportError:
            self.logger.warning("playsound library not available - sound notifications disabled")
            return False
        except Exception as e:
            self.logger.warning(f"Sound system not available: {e}")
            return False
    
    def _play_system_beep(self):
        """Play system beep as fallback"""
        try:
            if sys.platform.startswith('win'):
                # Windows system beep
                import winsound
                winsound.Beep(1000, 500)  # 1000Hz for 500ms
            else:
                # Unix/Linux system beep
                print('\a', end='', flush=True)
        except Exception as e:
            self.logger.debug(f"System beep failed: {e}")
    
    def _play_sound_file(self, sound_file: str):
        """Play a sound file using playsound"""
        try:
            import playsound
            # Check if file exists and is not empty
            if os.path.exists(sound_file) and os.path.getsize(sound_file) > 0:
                # Try to play the sound file
                playsound.playsound(sound_file, block=False)
                self.logger.debug(f"Successfully played sound file: {sound_file}")
                return True
            else:
                self.logger.debug(f"Sound file not found or empty: {sound_file}")
                return False
        except Exception as e:
            self.logger.debug(f"Failed to play sound file {sound_file}: {e}")
            return False
    
    def _play_notification_async(self, signal_type: str):
        """Play notification sound in a separate thread to avoid blocking"""
        try:
            # Try to play custom sound files first
            sound_files = {
                'CALL': 'sounds/call_signal.wav',
                'PUT': 'sounds/put_signal.wav',
                'TRADE': 'sounds/trade_signal.wav'
            }
            
            # Try specific sound file for signal type
            if signal_type in sound_files:
                if self._play_sound_file(sound_files[signal_type]):
                    return
            
            # Try generic trade signal sound
            if self._play_sound_file(sound_files['TRADE']):
                return
            
            # Fallback to system beep
            self._play_system_beep()
            
        except Exception as e:
            self.logger.debug(f"Notification sound failed: {e}")
    
    def play_signal_notification(self, signal_type: str, confidence: float):
        """
        Play notification sound for trading signal
        
        Args:
            signal_type: Type of signal ('CALL', 'PUT', etc.)
            confidence: Signal confidence level (0.0 to 1.0)
        """
        if not self.enabled or not self._sound_available:
            return
        
        # Only play notification for high confidence signals (70%+)
        if confidence < 0.50:
            return
        
        try:
            # Play sound in separate thread to avoid blocking the main application
            sound_thread = threading.Thread(
                target=self._play_notification_async,
                args=(signal_type,),
                daemon=True
            )
            sound_thread.start()
            
            self.logger.info(f"Playing notification sound for {signal_type} signal ({confidence:.1%} confidence)")
            
        except Exception as e:
            self.logger.error(f"Failed to play notification sound: {e}")
    
    def enable(self):
        """Enable sound notifications"""
        self.enabled = True
        self.logger.info("Sound notifications enabled")
    
    def disable(self):
        """Disable sound notifications"""
        self.enabled = False
        self.logger.info("Sound notifications disabled")
    
    def is_enabled(self) -> bool:
        """Check if sound notifications are enabled"""
        return self.enabled and self._sound_available
    
    def test_sound(self):
        """Test sound notification system"""
        if not self._sound_available:
            print("Sound system not available")
            return False
        
        print("Testing notification sound...")
        try:
            self._play_notification_async('TEST')
            print("Sound test completed")
            return True
        except Exception as e:
            print(f"Sound test failed: {e}")
            return False

# Global notification sound instance
_notification_sound = None

def get_notification_sound() -> NotificationSound:
    """Get global notification sound instance"""
    global _notification_sound
    if _notification_sound is None:
        _notification_sound = NotificationSound()
    return _notification_sound

def play_signal_notification(signal_type: str, confidence: float):
    """Convenience function to play signal notification"""
    notification_sound = get_notification_sound()
    notification_sound.play_signal_notification(signal_type, confidence)

def enable_notifications():
    """Enable sound notifications"""
    notification_sound = get_notification_sound()
    notification_sound.enable()

def disable_notifications():
    """Disable sound notifications"""
    notification_sound = get_notification_sound()
    notification_sound.disable()

def test_notification_sound():
    """Test the notification sound system"""
    notification_sound = get_notification_sound()
    return notification_sound.test_sound()
