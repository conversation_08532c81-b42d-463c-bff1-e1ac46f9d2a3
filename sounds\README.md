# Sound Notifications

This directory contains sound files for trading signal notifications.

## Sound Files

The bot will look for these sound files in order of preference:

1. **call_signal.wav** - Played for CALL signals with 70%+ confidence
2. **put_signal.wav** - Played for PUT signals with 70%+ confidence  
3. **trade_signal.wav** - Generic sound for any trading signal with 70%+ confidence

## Fallback Behavior

If no sound files are found, the bot will use the system beep as a fallback notification.

## Adding Your Own Sounds

1. Place your sound files in this directory
2. Use WAV format for best compatibility
3. Keep files small (under 1MB) for quick playback
4. Recommended duration: 0.5-2 seconds

## Supported Formats

- WAV (recommended)
- MP3 (may require additional codecs)
- Other formats supported by your system

## Disabling Sounds

Sound notifications can be disabled by setting `ENABLE_SOUND_NOTIFICATIONS=false` in your .env file or through the bot's configuration.

## Testing

You can test the sound system using the notification sound test function in the bot.
