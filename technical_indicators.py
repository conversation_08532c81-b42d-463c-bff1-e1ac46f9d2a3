"""
Technical Indicators Module
Calculates technical indicators for binary options trading analysis
"""
import pandas as pd
import numpy as np
from typing import Dict, Optional
import pandas_ta as ta
from logger_config import setup_logger, log_error


class TechnicalIndicators:
    """
    Technical indicators calculator for market analysis
    Uses pandas-ta library for comprehensive technical analysis
    """
    
    def __init__(self):
        self.logger = setup_logger("TechnicalIndicators", show_console=False)
    
    def calculate_all_indicators(self, df: pd.DataFrame, symbol: str = "") -> Dict:
        """
        Calculate all relevant technical indicators for binary options trading
        
        Args:
            df: DataFrame with OHLC data (columns: open, high, low, close)
            symbol: Symbol name for logging
            
        Returns:
            Dictionary containing all calculated indicators
        """
        try:
            if df is None or df.empty or len(df) < 20:
                self.logger.warning(f"Insufficient data for indicators calculation: {len(df) if df is not None else 0} rows")
                return {}
            
            # Ensure we have the required columns
            required_cols = ['open', 'high', 'low', 'close']
            if not all(col in df.columns for col in required_cols):
                self.logger.error(f"Missing required OHLC columns. Available: {list(df.columns)}")
                return {}
            
            indicators = {}
            
            # Trend Indicators
            indicators.update(self._calculate_trend_indicators(df))
            
            # Momentum Indicators  
            indicators.update(self._calculate_momentum_indicators(df))
            
            # Volatility Indicators
            indicators.update(self._calculate_volatility_indicators(df))
            
            # Volume Indicators (if volume data available)
            if 'volume' in df.columns:
                indicators.update(self._calculate_volume_indicators(df))
            
            self.logger.info(f"Calculated {len(indicators)} technical indicators for {symbol}")
            return indicators
            
        except Exception as e:
            log_error(self.logger, e, f"calculating indicators for {symbol}")
            return {}
    
    def _calculate_trend_indicators(self, df: pd.DataFrame) -> Dict:
        """Calculate trend-based indicators"""
        indicators = {}
        
        try:
            # Simple Moving Averages
            indicators['SMA_10'] = ta.sma(df['close'], length=10)
            indicators['SMA_20'] = ta.sma(df['close'], length=20)
            indicators['SMA_50'] = ta.sma(df['close'], length=min(50, len(df)-1))
            
            # Exponential Moving Averages
            indicators['EMA_10'] = ta.ema(df['close'], length=10)
            indicators['EMA_20'] = ta.ema(df['close'], length=20)
            
            # MACD
            macd_data = ta.macd(df['close'])
            if macd_data is not None and not macd_data.empty:
                indicators['MACD'] = macd_data['MACD_12_26_9']
                indicators['MACD_Signal'] = macd_data['MACDs_12_26_9']
                indicators['MACD_Histogram'] = macd_data['MACDh_12_26_9']
            
            # Parabolic SAR
            psar = ta.psar(df['high'], df['low'])
            if psar is not None and not psar.empty:
                indicators['PSAR'] = psar['PSARl_0.02_0.2'].fillna(psar['PSARs_0.02_0.2'])
            
        except Exception as e:
            self.logger.error(f"Error calculating trend indicators: {e}")
        
        return indicators
    
    def _calculate_momentum_indicators(self, df: pd.DataFrame) -> Dict:
        """Calculate momentum-based indicators"""
        indicators = {}
        
        try:
            # RSI
            indicators['RSI'] = ta.rsi(df['close'], length=14)
            
            # Stochastic Oscillator
            stoch = ta.stoch(df['high'], df['low'], df['close'])
            if stoch is not None and not stoch.empty:
                indicators['STOCH_K'] = stoch['STOCHk_14_3_3']
                indicators['STOCH_D'] = stoch['STOCHd_14_3_3']
            
            # Williams %R
            indicators['WILLIAMS_R'] = ta.willr(df['high'], df['low'], df['close'])
            
            # Commodity Channel Index
            indicators['CCI'] = ta.cci(df['high'], df['low'], df['close'])
            
            # Rate of Change
            indicators['ROC'] = ta.roc(df['close'], length=10)
            
        except Exception as e:
            self.logger.error(f"Error calculating momentum indicators: {e}")
        
        return indicators
    
    def _calculate_volatility_indicators(self, df: pd.DataFrame) -> Dict:
        """Calculate volatility-based indicators"""
        indicators = {}
        
        try:
            # Bollinger Bands
            bb = ta.bbands(df['close'], length=20)
            if bb is not None and not bb.empty:
                indicators['BB_Upper'] = bb['BBU_20_2.0']
                indicators['BB_Middle'] = bb['BBM_20_2.0']
                indicators['BB_Lower'] = bb['BBL_20_2.0']
                indicators['BB_Width'] = bb['BBB_20_2.0']
                indicators['BB_Percent'] = bb['BBP_20_2.0']
            
            # Average True Range
            indicators['ATR'] = ta.atr(df['high'], df['low'], df['close'])
            
            # Keltner Channels
            kc = ta.kc(df['high'], df['low'], df['close'])
            if kc is not None and not kc.empty:
                indicators['KC_Upper'] = kc['KCUe_20_2']
                indicators['KC_Middle'] = kc['KCBe_20_2']
                indicators['KC_Lower'] = kc['KCLe_20_2']
            
        except Exception as e:
            self.logger.error(f"Error calculating volatility indicators: {e}")
        
        return indicators
    
    def _calculate_volume_indicators(self, df: pd.DataFrame) -> Dict:
        """Calculate volume-based indicators"""
        indicators = {}
        
        try:
            # Volume SMA
            indicators['Volume_SMA'] = ta.sma(df['volume'], length=20)
            
            # On Balance Volume
            indicators['OBV'] = ta.obv(df['close'], df['volume'])
            
            # Volume Weighted Average Price
            indicators['VWAP'] = ta.vwap(df['high'], df['low'], df['close'], df['volume'])
            
        except Exception as e:
            self.logger.error(f"Error calculating volume indicators: {e}")
        
        return indicators
    
    def get_indicator_summary(self, indicators: Dict, current_price: float) -> Dict:
        """
        Get a summary of indicator signals for AI analysis
        
        Args:
            indicators: Dictionary of calculated indicators
            current_price: Current market price
            
        Returns:
            Dictionary with indicator signals and interpretations
        """
        summary = {
            'trend_signals': [],
            'momentum_signals': [],
            'volatility_signals': [],
            'support_resistance': {}
        }
        
        try:
            # Trend Analysis
            if 'SMA_20' in indicators and not indicators['SMA_20'].empty:
                sma_20 = indicators['SMA_20'].iloc[-1]
                if current_price > sma_20:
                    summary['trend_signals'].append("Price above SMA20 (bullish)")
                else:
                    summary['trend_signals'].append("Price below SMA20 (bearish)")
            
            # MACD Analysis
            if 'MACD' in indicators and 'MACD_Signal' in indicators:
                macd = indicators['MACD'].iloc[-1] if not indicators['MACD'].empty else None
                signal = indicators['MACD_Signal'].iloc[-1] if not indicators['MACD_Signal'].empty else None
                
                if macd is not None and signal is not None:
                    if macd > signal:
                        summary['trend_signals'].append("MACD bullish crossover")
                    else:
                        summary['trend_signals'].append("MACD bearish crossover")
            
            # RSI Analysis
            if 'RSI' in indicators and not indicators['RSI'].empty:
                rsi = indicators['RSI'].iloc[-1]
                if rsi > 70:
                    summary['momentum_signals'].append(f"RSI overbought ({rsi:.1f})")
                elif rsi < 30:
                    summary['momentum_signals'].append(f"RSI oversold ({rsi:.1f})")
                else:
                    summary['momentum_signals'].append(f"RSI neutral ({rsi:.1f})")
            
            # Bollinger Bands Analysis
            if all(key in indicators for key in ['BB_Upper', 'BB_Lower', 'BB_Percent']):
                bb_upper = indicators['BB_Upper'].iloc[-1] if not indicators['BB_Upper'].empty else None
                bb_lower = indicators['BB_Lower'].iloc[-1] if not indicators['BB_Lower'].empty else None
                bb_percent = indicators['BB_Percent'].iloc[-1] if not indicators['BB_Percent'].empty else None
                
                if bb_upper and bb_lower:
                    summary['support_resistance']['bb_upper'] = bb_upper
                    summary['support_resistance']['bb_lower'] = bb_lower
                    
                    if bb_percent is not None:
                        if bb_percent > 0.8:
                            summary['volatility_signals'].append("Price near BB upper band")
                        elif bb_percent < 0.2:
                            summary['volatility_signals'].append("Price near BB lower band")
            
        except Exception as e:
            self.logger.error(f"Error creating indicator summary: {e}")
        
        return summary
