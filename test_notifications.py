#!/usr/bin/env python3
"""
Test script for notification sound system
"""
import time
from notification_sound import test_notification_sound, play_signal_notification
from deriv_config import DerivConfig

def main():
    """Test the notification sound system"""
    print("Binary Options Bot - Sound Notification Test")
    print("=" * 50)
    
    # Display current configuration
    print(f"Sound Notifications: {'Enabled' if DerivConfig.ENABLE_SOUND_NOTIFICATIONS else 'Disabled'}")
    print(f"Notification Threshold: {DerivConfig.SOUND_NOTIFICATION_THRESHOLD:.0%}")
    print()
    
    if not DerivConfig.ENABLE_SOUND_NOTIFICATIONS:
        print("Sound notifications are disabled in configuration.")
        print("Set ENABLE_SOUND_NOTIFICATIONS=true in your .env file to enable them.")
        return
    
    # Test basic sound system
    print("Testing basic sound system...")
    if test_notification_sound():
        print("✓ Basic sound system working")
    else:
        print("✗ Basic sound system failed")
        print("Make sure you have the 'playsound' library installed:")
        print("pip install playsound==1.3.0")
        return
    
    print()
    
    # Test different signal types with various confidence levels
    threshold = DerivConfig.SOUND_NOTIFICATION_THRESHOLD
    test_cases = [
        ("CALL", threshold - 0.05, f"Below threshold ({threshold:.0%}) - should NOT play"),
        ("CALL", threshold, f"Exactly at threshold ({threshold:.0%}) - should play"),
        ("CALL", threshold + 0.01, f"Just above threshold ({threshold:.0%}) - should play"),
        ("PUT", threshold + 0.05, f"Above threshold ({threshold:.0%}) - should play"),
        ("PUT", threshold - 0.01, f"Just below threshold ({threshold:.0%}) - should NOT play"),
        ("CALL", 0.95, f"High confidence (95%) - should play"),
    ]

    print(f"Testing signal notifications with {threshold:.0%} threshold...")
    for signal_type, confidence, description in test_cases:
        print(f"Testing {signal_type} signal with {confidence:.0%} confidence ({description})")
        play_signal_notification(signal_type, confidence, threshold)
        time.sleep(2)  # Wait between tests
    
    print()
    print("Sound notification test completed!")
    print()
    print(f"If you heard sounds for signals with {threshold:.0%}+ confidence, the system is working correctly.")
    print("The system will play notification sounds for ANY confidence level at or above the threshold.")
    print(f"Examples: {threshold:.0%} = PLAY, {threshold+0.01:.0%} = PLAY, {threshold+0.05:.0%} = PLAY, {threshold-0.01:.0%} = NO SOUND")
    print()
    print("If no sounds played, check:")
    print("1. Your system volume is turned up")
    print("2. You have speakers/headphones connected")
    print("3. The playsound library is properly installed")
    print("4. Your system supports audio playback")
    print()
    print(f"To change the threshold, set SOUND_NOTIFICATION_THRESHOLD in your .env file (current: {threshold:.2f})")

if __name__ == "__main__":
    main()
