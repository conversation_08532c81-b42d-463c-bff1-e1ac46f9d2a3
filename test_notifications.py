#!/usr/bin/env python3
"""
Test script for notification sound system
"""
import time
from notification_sound import test_notification_sound, play_signal_notification
from deriv_config import DerivConfig

def main():
    """Test the notification sound system"""
    print("Binary Options Bot - Sound Notification Test")
    print("=" * 50)
    
    # Display current configuration
    print(f"Sound Notifications: {'Enabled' if DerivConfig.ENABLE_SOUND_NOTIFICATIONS else 'Disabled'}")
    print(f"Notification Threshold: {DerivConfig.SOUND_NOTIFICATION_THRESHOLD:.0%}")
    print()
    
    if not DerivConfig.ENABLE_SOUND_NOTIFICATIONS:
        print("Sound notifications are disabled in configuration.")
        print("Set ENABLE_SOUND_NOTIFICATIONS=true in your .env file to enable them.")
        return
    
    # Test basic sound system
    print("Testing basic sound system...")
    if test_notification_sound():
        print("✓ Basic sound system working")
    else:
        print("✗ Basic sound system failed")
        print("Make sure you have the 'playsound' library installed:")
        print("pip install playsound==1.3.0")
        return
    
    print()
    
    # Test different signal types with various confidence levels
    test_cases = [
        ("CALL", 0.65, "Below threshold - should not play"),
        ("CALL", 0.75, "Above threshold - should play"),
        ("PUT", 0.80, "Above threshold - should play"),
        ("PUT", 0.69, "Below threshold - should not play"),
        ("CALL", 0.95, "High confidence - should play"),
    ]
    
    print("Testing signal notifications...")
    for signal_type, confidence, description in test_cases:
        print(f"Testing {signal_type} signal with {confidence:.0%} confidence ({description})")
        play_signal_notification(signal_type, confidence)
        time.sleep(2)  # Wait between tests
    
    print()
    print("Sound notification test completed!")
    print()
    print("If you heard sounds for signals with 70%+ confidence, the system is working correctly.")
    print("If no sounds played, check:")
    print("1. Your system volume is turned up")
    print("2. You have speakers/headphones connected")
    print("3. The playsound library is properly installed")
    print("4. Your system supports audio playback")

if __name__ == "__main__":
    main()
