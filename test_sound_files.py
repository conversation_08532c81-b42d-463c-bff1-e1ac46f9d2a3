#!/usr/bin/env python3
"""
Test script to verify sound files are playable
"""
import os
import sys
import time

def test_sound_file(file_path):
    """Test if a sound file can be played"""
    print(f"Testing sound file: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"  ❌ File not found: {file_path}")
        return False
    
    file_size = os.path.getsize(file_path)
    print(f"  📁 File size: {file_size} bytes")
    
    if file_size == 0:
        print(f"  ❌ File is empty")
        return False
    
    # Test with Windows winsound
    try:
        import winsound
        abs_path = os.path.abspath(file_path)
        print(f"  🔊 Playing with winsound: {abs_path}")
        winsound.PlaySound(abs_path, winsound.SND_FILENAME | winsound.SND_ASYNC)
        print(f"  ✅ Successfully played with winsound")
        return True
    except Exception as e:
        print(f"  ❌ Failed to play with winsound: {e}")
        
        # Try with playsound as fallback
        try:
            import playsound
            print(f"  🔊 Trying with playsound...")
            playsound.playsound(abs_path, block=False)
            print(f"  ✅ Successfully played with playsound")
            return True
        except Exception as e2:
            print(f"  ❌ Failed to play with playsound: {e2}")
            return False

def main():
    """Test all sound files"""
    print("Sound File Test")
    print("=" * 50)
    
    sound_files = [
        "sounds/call_signal.wav",
        "sounds/put_signal.wav",
        "sounds/trade_signal.wav"  # Optional file
    ]
    
    working_files = 0
    total_files = 0
    
    for sound_file in sound_files:
        total_files += 1
        print()
        if test_sound_file(sound_file):
            working_files += 1
            time.sleep(3)  # Wait to hear the sound
        else:
            if sound_file == "sounds/trade_signal.wav":
                print("  ℹ️  This file is optional")
            else:
                print("  ⚠️  This file is required for notifications")
    
    print()
    print("=" * 50)
    print(f"Results: {working_files}/{total_files} sound files working")
    
    if working_files >= 2:  # call_signal.wav and put_signal.wav
        print("✅ Sound system should work correctly!")
    else:
        print("❌ Sound system may have issues")
        print()
        print("Troubleshooting:")
        print("1. Make sure your .wav files are in standard PCM format")
        print("2. Try converting them to 16-bit PCM WAV format")
        print("3. Keep file sizes reasonable (under 1MB)")
        print("4. Test files in Windows Media Player first")

if __name__ == "__main__":
    main()
